package com.xlc.excel.example;

import com.xlc.excel.dto.ExcelImportResult;
import com.xlc.excel.enums.ExcelTemplateEnum;
import com.xlc.excel.model.UserInfo;
import com.xlc.excel.service.ExcelService;
import com.xlc.excel.service.ExcelExportService.ExcelExportResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Excel导入导出示例程序
 * 演示如何使用Excel服务进行导入导出操作
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
@Component
public class ExcelExample implements CommandLineRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(ExcelExample.class);
    
    @Autowired
    private ExcelService excelService;
    
    @Override
    public void run(String... args) throws Exception {
        logger.info("开始运行Excel导入导出示例程序");
        
        try {
            // 示例1：导出用户数据到Excel文件
            exportUserDataExample();
            
            // 示例2：从Excel文件导入用户数据
            importUserDataExample();
            
            // 示例3：导出Excel模板文件
            exportTemplateExample();
            
            // 示例4：预览Excel文件内容
            previewExcelExample();
            
            // 示例5：验证Excel文件格式
            validateExcelExample();
            
            // 示例6：获取Excel文件信息
            getFileInfoExample();
            
            // 示例7：服务健康检查
            healthCheckExample();
            
        } catch (Exception e) {
            logger.error("示例程序运行失败：{}", e.getMessage(), e);
        }
        
        logger.info("Excel导入导出示例程序运行完成");
    }
    
    /**
     * 示例1：导出用户数据到Excel文件
     */
    private void exportUserDataExample() throws IOException {
        logger.info("=== 示例1：导出用户数据到Excel文件 ===");
        
        // 准备测试数据
        List<UserInfo> userList = createSampleUserData(20);
        
        // 导出到文件
        try (FileOutputStream outputStream = new FileOutputStream("用户数据导出示例.xlsx")) {
            ExcelExportResult result = excelService.exportByTableName(
                    outputStream,
                    "用户数据导出示例.xlsx",
                    "user_info",
                    userList,
                    UserInfo.class
            );
            
            if (result.isSuccess()) {
                logger.info("导出成功：{}", result.getSummary());
            } else {
                logger.error("导出失败：{}", result.getErrorMessage());
            }
        }
    }
    
    /**
     * 示例2：从Excel文件导入用户数据
     */
    private void importUserDataExample() throws IOException {
        logger.info("=== 示例2：从Excel文件导入用户数据 ===");
        
        try (FileInputStream inputStream = new FileInputStream("用户数据导出示例.xlsx")) {
            ExcelImportResult<UserInfo> result = excelService.importByTableName(
                    inputStream,
                    "用户数据导出示例.xlsx",
                    "user_info",
                    UserInfo.class
            );
            
            if (result.isSuccess()) {
                logger.info("导入成功：{}", result.getSummary());
                logger.info("导入的用户数据：");
                result.getSuccessData().forEach(user -> 
                    logger.info("  - {}: {}, {}, {}", user.getUserId(), user.getUsername(), user.getRealName(), user.getEmail())
                );
            } else {
                logger.error("导入失败，错误信息：");
                result.getErrors().forEach(error -> 
                    logger.error("  - {}", error.getFullErrorDescription())
                );
            }
        } catch (Exception e) {
            logger.warn("导入示例跳过（文件可能不存在）：{}", e.getMessage());
        }
    }
    
    /**
     * 示例3：导出Excel模板文件
     */
    private void exportTemplateExample() throws IOException {
        logger.info("=== 示例3：导出Excel模板文件 ===");
        
        try (FileOutputStream outputStream = new FileOutputStream("用户信息模板.xlsx")) {
            ExcelExportResult result = excelService.exportTemplate(
                    outputStream,
                    "用户信息模板.xlsx",
                    ExcelTemplateEnum.USER_INFO
            );
            
            if (result.isSuccess()) {
                logger.info("模板导出成功：{}", result.getSummary());
            } else {
                logger.error("模板导出失败：{}", result.getErrorMessage());
            }
        }
    }
    
    /**
     * 示例4：预览Excel文件内容
     */
    private void previewExcelExample() throws IOException {
        logger.info("=== 示例4：预览Excel文件内容 ===");
        
        try (FileInputStream inputStream = new FileInputStream("用户数据导出示例.xlsx")) {
            ExcelImportResult<UserInfo> result = excelService.previewExcel(
                    inputStream,
                    "用户数据导出示例.xlsx",
                    UserInfo.class,
                    5 // 只预览前5行
            );
            
            if (result.isSuccess()) {
                logger.info("预览成功：{}", result.getSummary());
                logger.info("预览的用户数据：");
                result.getSuccessData().forEach(user -> 
                    logger.info("  - {}: {}, {}", user.getUserId(), user.getUsername(), user.getRealName())
                );
            } else {
                logger.error("预览失败");
            }
        } catch (Exception e) {
            logger.warn("预览示例跳过（文件可能不存在）：{}", e.getMessage());
        }
    }
    
    /**
     * 示例5：验证Excel文件格式
     */
    private void validateExcelExample() throws IOException {
        logger.info("=== 示例5：验证Excel文件格式 ===");
        
        try (FileInputStream inputStream = new FileInputStream("用户数据导出示例.xlsx")) {
            boolean isValid = excelService.validateExcelFormat(
                    inputStream,
                    "用户数据导出示例.xlsx",
                    "user_info"
            );
            
            logger.info("文件格式验证结果：{}", isValid ? "有效" : "无效");
        } catch (Exception e) {
            logger.warn("验证示例跳过（文件可能不存在）：{}", e.getMessage());
        }
    }
    
    /**
     * 示例6：获取Excel文件信息
     */
    private void getFileInfoExample() throws IOException {
        logger.info("=== 示例6：获取Excel文件信息 ===");
        
        try (FileInputStream inputStream = new FileInputStream("用户数据导出示例.xlsx")) {
            ExcelImportService.ExcelFileInfo fileInfo = excelService.getExcelFileInfo(
                    inputStream,
                    "用户数据导出示例.xlsx"
            );
            
            if (fileInfo.isValid()) {
                logger.info("文件信息获取成功：");
                logger.info("  - 文件名：{}", fileInfo.getFileName());
                logger.info("  - 文件类型：{}", fileInfo.getFileType());
                logger.info("  - 文件大小：{}", fileInfo.getFileSize());
                logger.info("  - 工作表数量：{}", fileInfo.getSheetCount());
                logger.info("  - 总行数：{}", fileInfo.getTotalRows());
                logger.info("  - 总列数：{}", fileInfo.getTotalColumns());
            } else {
                logger.error("获取文件信息失败：{}", fileInfo.getErrorMessage());
            }
        } catch (Exception e) {
            logger.warn("文件信息示例跳过（文件可能不存在）：{}", e.getMessage());
        }
    }
    
    /**
     * 示例7：服务健康检查
     */
    private void healthCheckExample() {
        logger.info("=== 示例7：服务健康检查 ===");
        
        var healthInfo = excelService.getServiceHealth();
        
        logger.info("服务健康状态：");
        logger.info("  - 状态：{}", healthInfo.getStatus());
        logger.info("  - 是否健康：{}", healthInfo.isHealthy());
        logger.info("  - 版本：{}", healthInfo.getVersion());
        logger.info("  - 运行时间：{}ms", healthInfo.getUptime());
        logger.info("  - 活跃导入任务：{}", healthInfo.getActiveImportTasks());
        logger.info("  - 活跃导出任务：{}", healthInfo.getActiveExportTasks());
        
        if (healthInfo.getLastError() != null) {
            logger.info("  - 最后错误：{}", healthInfo.getLastError());
            logger.info("  - 错误时间：{}", healthInfo.getLastErrorTime());
        }
    }
    
    /**
     * 创建示例用户数据
     */
    private List<UserInfo> createSampleUserData(int count) {
        List<UserInfo> userList = new ArrayList<>();
        
        for (long i = 1; i <= count; i++) {
            userList.add(UserInfo.createSample(i, "user" + i));
        }
        
        logger.info("创建了{}条示例用户数据", count);
        return userList;
    }
}
