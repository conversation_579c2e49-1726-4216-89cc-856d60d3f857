package com.xlc.excel.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Excel导入结果封装类
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExcelImportResult<T> {
    
    /**
     * 导入是否成功
     */
    private boolean success;
    
    /**
     * 总行数（包含表头）
     */
    private int totalRows;
    
    /**
     * 数据行数（不包含表头）
     */
    private int dataRows;
    
    /**
     * 成功导入的行数
     */
    private int successRows;
    
    /**
     * 失败的行数
     */
    private int failureRows;
    
    /**
     * 跳过的行数（空行等）
     */
    private int skippedRows;
    
    /**
     * 成功导入的数据列表
     */
    @Builder.Default
    private List<T> successData = new ArrayList<>();
    
    /**
     * 失败的数据列表（包含错误信息）
     */
    @Builder.Default
    private List<ExcelRowData<T>> failureData = new ArrayList<>();
    
    /**
     * 错误信息列表
     */
    @Builder.Default
    private List<ExcelErrorInfo> errors = new ArrayList<>();
    
    /**
     * 导入开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 导入结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 导入耗时（毫秒）
     */
    private long duration;
    
    /**
     * 导入的文件名
     */
    private String fileName;
    
    /**
     * 导入的表名或模板标识
     */
    private String tableNameOrTemplate;
    
    /**
     * 额外的消息信息
     */
    private String message;
    
    /**
     * 是否有错误
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * 是否有失败数据
     */
    public boolean hasFailureData() {
        return failureData != null && !failureData.isEmpty();
    }
    
    /**
     * 获取错误数量
     */
    public int getErrorCount() {
        return errors != null ? errors.size() : 0;
    }
    
    /**
     * 添加错误信息
     */
    public void addError(ExcelErrorInfo error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
    }

    /**
     * 添加多个错误信息
     */
    public void addErrors(List<ExcelErrorInfo> errorList) {
        if (errorList != null && !errorList.isEmpty()) {
            if (errors == null) {
                errors = new ArrayList<>();
            }
            errors.addAll(errorList);
        }
    }
    
    /**
     * 添加成功数据
     */
    public void addSuccessData(T data) {
        if (successData == null) {
            successData = new ArrayList<>();
        }
        successData.add(data);
    }
    
    /**
     * 添加失败数据
     */
    public void addFailureData(ExcelRowData<T> rowData) {
        if (failureData == null) {
            failureData = new ArrayList<>();
        }
        failureData.add(rowData);
    }
    
    /**
     * 计算成功率
     */
    public double getSuccessRate() {
        if (dataRows == 0) {
            return 0.0;
        }
        return (double) successRows / dataRows * 100;
    }
    
    /**
     * 获取导入摘要信息
     */
    public String getSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("导入摘要：");
        sb.append("总行数=").append(totalRows);
        sb.append(", 数据行数=").append(dataRows);
        sb.append(", 成功=").append(successRows);
        sb.append(", 失败=").append(failureRows);
        sb.append(", 跳过=").append(skippedRows);
        sb.append(", 成功率=").append(String.format("%.2f%%", getSuccessRate()));
        sb.append(", 耗时=").append(duration).append("ms");
        
        if (hasErrors()) {
            sb.append(", 错误数=").append(getErrorCount());
        }
        
        return sb.toString();
    }
    
    /**
     * 创建成功的导入结果
     */
    public static <T> ExcelImportResult<T> success(List<T> data, String fileName, String tableNameOrTemplate) {
        return ExcelImportResult.<T>builder()
                .success(true)
                .successData(data)
                .successRows(data.size())
                .dataRows(data.size())
                .totalRows(data.size() + 1) // 假设有一行表头
                .failureRows(0)
                .skippedRows(0)
                .fileName(fileName)
                .tableNameOrTemplate(tableNameOrTemplate)
                .endTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建失败的导入结果
     */
    public static <T> ExcelImportResult<T> failure(String errorMessage, String fileName, String tableNameOrTemplate) {
        ExcelImportResult<T> result = ExcelImportResult.<T>builder()
                .success(false)
                .successRows(0)
                .dataRows(0)
                .totalRows(0)
                .failureRows(0)
                .skippedRows(0)
                .fileName(fileName)
                .tableNameOrTemplate(tableNameOrTemplate)
                .message(errorMessage)
                .endTime(LocalDateTime.now())
                .build();
        
        // 添加通用错误信息
        result.addError(ExcelErrorInfo.builder()
                .errorMessage(errorMessage)
                .errorType(ExcelErrorInfo.ErrorType.OTHER_ERROR)
                .errorDetail(errorMessage)
                .build());
        
        return result;
    }
    
    /**
     * 设置导入完成时间并计算耗时
     */
    public void setImportCompleted() {
        this.endTime = LocalDateTime.now();
        if (this.startTime != null) {
            this.duration = java.time.Duration.between(this.startTime, this.endTime).toMillis();
        }
    }
    
    /**
     * 设置导入开始时间
     */
    public void setImportStarted() {
        this.startTime = LocalDateTime.now();
    }
}
