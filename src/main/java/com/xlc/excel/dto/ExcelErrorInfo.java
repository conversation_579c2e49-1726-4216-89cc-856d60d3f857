package com.xlc.excel.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Excel错误信息封装类
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExcelErrorInfo {
    
    /**
     * 错误行号（从1开始）
     */
    private Integer rowIndex;
    
    /**
     * 错误列号（从1开始）
     */
    private Integer columnIndex;
    
    /**
     * 列名
     */
    private String columnName;
    
    /**
     * 错误的数据值
     */
    private Object errorValue;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 错误类型
     */
    private ErrorType errorType;
    
    /**
     * 错误详细描述
     */
    private String errorDetail;
    
    /**
     * 错误类型枚举
     */
    public enum ErrorType {
        /**
         * 数据格式错误
         */
        FORMAT_ERROR("数据格式错误"),

        /**
         * 必填字段为空
         */
        REQUIRED_FIELD_EMPTY("必填字段为空"),

        /**
         * 数据长度超限
         */
        LENGTH_EXCEEDED("数据长度超限"),

        /**
         * 数据类型错误
         */
        TYPE_ERROR("数据类型错误"),

        /**
         * 数据校验失败
         */
        VALIDATION_ERROR("数据校验失败"),

        /**
         * 业务逻辑错误
         */
        BUSINESS_ERROR("业务逻辑错误"),

        /**
         * 空值错误
         */
        NULL_ERROR("空值错误"),

        /**
         * 解析错误
         */
        PARSE_ERROR("解析错误"),

        /**
         * 其他错误
         */
        OTHER_ERROR("其他错误");
        
        private final String description;
        
        ErrorType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 创建格式错误信息
     */
    public static ExcelErrorInfo formatError(Integer rowIndex, Integer columnIndex, String columnName, Object errorValue, String errorMessage) {
        return ExcelErrorInfo.builder()
                .rowIndex(rowIndex)
                .columnIndex(columnIndex)
                .columnName(columnName)
                .errorValue(errorValue)
                .errorMessage(errorMessage)
                .errorType(ErrorType.FORMAT_ERROR)
                .errorDetail(String.format("第%d行第%d列[%s]数据格式错误：%s", rowIndex, columnIndex, columnName, errorMessage))
                .build();
    }
    
    /**
     * 创建必填字段为空错误信息
     */
    public static ExcelErrorInfo requiredFieldEmptyError(Integer rowIndex, Integer columnIndex, String columnName) {
        return ExcelErrorInfo.builder()
                .rowIndex(rowIndex)
                .columnIndex(columnIndex)
                .columnName(columnName)
                .errorValue(null)
                .errorMessage("必填字段不能为空")
                .errorType(ErrorType.REQUIRED_FIELD_EMPTY)
                .errorDetail(String.format("第%d行第%d列[%s]为必填字段，不能为空", rowIndex, columnIndex, columnName))
                .build();
    }
    
    /**
     * 创建数据长度超限错误信息
     */
    public static ExcelErrorInfo lengthExceededError(Integer rowIndex, Integer columnIndex, String columnName, Object errorValue, int maxLength) {
        return ExcelErrorInfo.builder()
                .rowIndex(rowIndex)
                .columnIndex(columnIndex)
                .columnName(columnName)
                .errorValue(errorValue)
                .errorMessage(String.format("数据长度超过最大限制%d", maxLength))
                .errorType(ErrorType.LENGTH_EXCEEDED)
                .errorDetail(String.format("第%d行第%d列[%s]数据长度超过最大限制%d", rowIndex, columnIndex, columnName, maxLength))
                .build();
    }
    
    /**
     * 创建数据类型错误信息
     */
    public static ExcelErrorInfo typeError(Integer rowIndex, Integer columnIndex, String columnName, Object errorValue, String expectedType) {
        return ExcelErrorInfo.builder()
                .rowIndex(rowIndex)
                .columnIndex(columnIndex)
                .columnName(columnName)
                .errorValue(errorValue)
                .errorMessage(String.format("数据类型错误，期望类型：%s", expectedType))
                .errorType(ErrorType.TYPE_ERROR)
                .errorDetail(String.format("第%d行第%d列[%s]数据类型错误，期望类型：%s", rowIndex, columnIndex, columnName, expectedType))
                .build();
    }
    
    /**
     * 创建校验错误信息
     */
    public static ExcelErrorInfo validationError(Integer rowIndex, Integer columnIndex, String columnName, Object errorValue, String errorMessage) {
        return ExcelErrorInfo.builder()
                .rowIndex(rowIndex)
                .columnIndex(columnIndex)
                .columnName(columnName)
                .errorValue(errorValue)
                .errorMessage(errorMessage)
                .errorType(ErrorType.VALIDATION_ERROR)
                .errorDetail(String.format("第%d行第%d列[%s]数据校验失败：%s", rowIndex, columnIndex, columnName, errorMessage))
                .build();
    }
    
    /**
     * 创建业务错误信息
     */
    public static ExcelErrorInfo businessError(Integer rowIndex, String errorMessage) {
        return ExcelErrorInfo.builder()
                .rowIndex(rowIndex)
                .errorMessage(errorMessage)
                .errorType(ErrorType.BUSINESS_ERROR)
                .errorDetail(String.format("第%d行业务逻辑错误：%s", rowIndex, errorMessage))
                .build();
    }

    /**
     * 创建必填错误信息
     */
    public static ExcelErrorInfo requiredError(Integer rowIndex, Integer columnIndex, String columnName, Object errorValue) {
        return ExcelErrorInfo.builder()
                .rowIndex(rowIndex)
                .columnIndex(columnIndex)
                .columnName(columnName)
                .errorValue(errorValue)
                .errorMessage("必填字段不能为空")
                .errorType(ErrorType.REQUIRED_FIELD_EMPTY)
                .errorDetail(String.format("第%d行第%d列[%s]为必填字段，不能为空", rowIndex, columnIndex, columnName))
                .build();
    }
    
    /**
     * 获取完整的错误描述
     */
    public String getFullErrorDescription() {
        if (errorDetail != null && !errorDetail.trim().isEmpty()) {
            return errorDetail;
        }
        
        StringBuilder sb = new StringBuilder();
        if (rowIndex != null) {
            sb.append("第").append(rowIndex).append("行");
        }
        if (columnIndex != null) {
            sb.append("第").append(columnIndex).append("列");
        }
        if (columnName != null && !columnName.trim().isEmpty()) {
            sb.append("[").append(columnName).append("]");
        }
        if (errorMessage != null && !errorMessage.trim().isEmpty()) {
            sb.append("：").append(errorMessage);
        }
        
        return sb.toString();
    }
}
