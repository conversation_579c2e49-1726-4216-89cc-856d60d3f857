package com.xlc.excel.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.xlc.excel.config.ExcelConfig;
import com.xlc.excel.dto.ExcelImportRequest;
import com.xlc.excel.dto.ExcelImportResult;
import com.xlc.excel.enums.ExcelTemplateEnum;
import com.xlc.excel.exception.ExcelException;
import com.xlc.excel.listener.ExcelDataListener;
import com.xlc.excel.service.ExcelImportService;
import com.xlc.excel.utils.ExcelUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * Excel导入服务实现类
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
@Service
public class ExcelImportServiceImpl implements ExcelImportService {
    
    private static final Logger logger = LoggerFactory.getLogger(ExcelImportServiceImpl.class);
    
    /**
     * 异步执行器
     */
    private final Executor asyncExecutor = Executors.newFixedThreadPool(5);
    
    @Override
    public <T> ExcelImportResult<T> importExcel(ExcelImportRequest request) {
        logger.info("开始导入Excel文件：{}", request.getFileName());
        
        try {
            // 验证请求参数
            request.validate();
            
            // 检查文件格式
            if (!ExcelUtils.isExcelFile(request.getFileName())) {
                throw new ExcelException("INVALID_FILE_FORMAT", "不支持的文件格式，请上传Excel文件");
            }
            
            // 创建数据监听器
            ExcelDataListener<T> listener = new ExcelDataListener<T>(
                    request.getFileName(),
                    request.getIdentifier(),
                    (Class<T>) request.getTargetClass(),
                    request.getConfig()
            );
            
            // 设置预览模式
            if (request.isPreviewMode()) {
                listener.setPreviewMode(true, request.getPreviewRows());
            }
            
            // 创建ExcelReader
            ExcelReader excelReader = EasyExcel.read(request.getInputStream(), request.getTargetClass(), listener)
                    .headRowNumber(request.getConfig().getHeaderRow() + 1) // EasyExcel从1开始计数
                    .build();
            
            try {
                // 读取指定工作表
                ReadSheet readSheet;
                if (request.getSheetName() != null) {
                    readSheet = EasyExcel.readSheet(request.getSheetName()).build();
                } else {
                    readSheet = EasyExcel.readSheet(request.getSheetIndex()).build();
                }
                
                // 开始读取
                excelReader.read(readSheet);
                
            } finally {
                // 这里千万别忘记关闭，读的时候会创建临时文件，到时磁盘会崩的
                excelReader.finish();
            }
            
            // 获取导入结果
            ExcelImportResult<T> result = listener.getImportResult();
            
            logger.info("Excel文件导入完成：{}", result.getSummary());
            
            return result;
            
        } catch (ExcelException e) {
            logger.error("Excel导入失败：{}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            logger.error("Excel导入过程中发生未知异常：{}", e.getMessage(), e);
            throw new ExcelException("IMPORT_ERROR", "Excel导入失败：" + e.getMessage(), e);
        }
    }
    
    @Override
    public <T> ExcelImportResult<T> importByTableName(InputStream inputStream, String fileName, String tableName, Class<T> targetClass) {
        ExcelImportRequest request = ExcelImportRequest.byTableName(inputStream, fileName, tableName, targetClass);
        return importExcel(request);
    }
    
    @Override
    public <T> ExcelImportResult<T> importByTemplate(InputStream inputStream, String fileName, ExcelTemplateEnum template, Class<T> targetClass) {
        ExcelImportRequest request = ExcelImportRequest.byTemplate(inputStream, fileName, template, targetClass);
        return importExcel(request);
    }
    
    @Override
    public <T> ExcelImportResult<T> previewExcel(InputStream inputStream, String fileName, Class<T> targetClass, int previewRows) {
        ExcelImportRequest request = ExcelImportRequest.forPreview(inputStream, fileName, targetClass, previewRows);
        return importExcel(request);
    }
    
    @Override
    public <T> ExcelImportResult<T> importStrict(InputStream inputStream, String fileName, String tableName, Class<T> targetClass) {
        ExcelImportRequest request = ExcelImportRequest.strictMode(inputStream, fileName, tableName, targetClass);
        return importExcel(request);
    }
    
    @Override
    public <T> ExcelImportResult<T> importLenient(InputStream inputStream, String fileName, String tableName, Class<T> targetClass) {
        ExcelImportRequest request = ExcelImportRequest.lenientMode(inputStream, fileName, tableName, targetClass);
        return importExcel(request);
    }
    
    @Override
    public <T> CompletableFuture<ExcelImportResult<T>> importExcelAsync(ExcelImportRequest request) {
        logger.info("开始异步导入Excel文件：{}", request.getFileName());
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                return importExcel(request);
            } catch (Exception e) {
                logger.error("异步导入Excel文件失败：{}", e.getMessage(), e);
                throw new RuntimeException(e);
            }
        }, asyncExecutor);
    }
    
    @Override
    public <T> ExcelImportResult<T> batchImport(ExcelImportRequest... requests) {
        logger.info("开始批量导入Excel文件，文件数量：{}", requests.length);
        
        if (requests == null || requests.length == 0) {
            throw new ExcelException("INVALID_PARAMETER", "批量导入请求不能为空");
        }
        
        // 创建合并的导入结果
        ExcelImportResult<T> mergedResult = ExcelImportResult.<T>builder()
                .startTime(LocalDateTime.now())
                .success(true)
                .build();
        
        int totalFiles = requests.length;
        int successFiles = 0;
        int failureFiles = 0;
        
        for (int i = 0; i < requests.length; i++) {
            ExcelImportRequest request = requests[i];
            
            try {
                logger.info("处理第{}个文件：{}", i + 1, request.getFileName());
                
                ExcelImportResult<T> singleResult = importExcel(request);
                
                // 合并结果
                mergeImportResults(mergedResult, singleResult);
                
                if (singleResult.isSuccess()) {
                    successFiles++;
                } else {
                    failureFiles++;
                }
                
            } catch (Exception e) {
                logger.error("处理第{}个文件失败：{}", i + 1, e.getMessage(), e);
                failureFiles++;
                
                // 添加文件级别的错误信息
                mergedResult.addError(com.xlc.excel.dto.ExcelErrorInfo.builder()
                        .errorMessage(String.format("文件[%s]导入失败：%s", request.getFileName(), e.getMessage()))
                        .errorType(com.xlc.excel.dto.ExcelErrorInfo.ErrorType.OTHER_ERROR)
                        .build());
            }
        }
        
        // 设置批量导入结果
        mergedResult.setImportCompleted();
        mergedResult.setSuccess(successFiles > 0);
        mergedResult.setMessage(String.format("批量导入完成：总文件数=%d，成功=%d，失败=%d", 
                totalFiles, successFiles, failureFiles));
        
        logger.info("批量导入Excel文件完成：{}", mergedResult.getMessage());
        
        return mergedResult;
    }
    
    @Override
    public boolean validateExcelFormat(InputStream inputStream, String fileName, ExcelTemplateEnum template) {
        try {
            // 检查文件格式
            if (!ExcelUtils.isExcelFile(fileName)) {
                return false;
            }
            
            // 这里可以添加更详细的格式验证逻辑
            // 例如：检查表头是否匹配模板要求
            
            return true;
            
        } catch (Exception e) {
            logger.error("验证Excel文件格式时发生异常：{}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean validateExcelFormat(InputStream inputStream, String fileName, String tableName) {
        try {
            // 检查文件格式
            if (!ExcelUtils.isExcelFile(fileName)) {
                return false;
            }
            
            // 根据表名获取对应的模板
            ExcelTemplateEnum template = ExcelTemplateEnum.getByTableName(tableName);
            if (template != null) {
                return validateExcelFormat(inputStream, fileName, template);
            }
            
            return true;
            
        } catch (Exception e) {
            logger.error("验证Excel文件格式时发生异常：{}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public ExcelFileInfo getExcelFileInfo(InputStream inputStream, String fileName) {
        ExcelFileInfo fileInfo = new ExcelFileInfo();
        fileInfo.setFileName(fileName);
        
        try {
            // 检查文件格式
            if (!ExcelUtils.isExcelFile(fileName)) {
                fileInfo.setValid(false);
                fileInfo.setErrorMessage("不支持的文件格式");
                return fileInfo;
            }
            
            fileInfo.setFileType(ExcelUtils.getExcelFileType(fileName));
            
            // 获取文件大小
            try {
                long fileSize = inputStream.available();
                fileInfo.setFileSize(ExcelUtils.formatFileSize(fileSize));
            } catch (Exception e) {
                logger.warn("获取文件大小失败：{}", e.getMessage());
            }
            
            // 读取Excel文件信息
            try {
                ExcelReader excelReader = EasyExcel.read(inputStream).build();
                
                try {
                    // 获取工作表信息
                    // 注意：EasyExcel 3.x版本的API可能有所不同
                    // 这里提供一个基本的实现框架
                    
                    fileInfo.setSheetCount(1); // 默认值，实际应该通过API获取
                    fileInfo.setSheetNames(new String[]{"Sheet1"}); // 默认值
                    fileInfo.setTotalRows(0); // 需要通过读取获取
                    fileInfo.setTotalColumns(0); // 需要通过读取获取
                    fileInfo.setValid(true);
                    
                } finally {
                    excelReader.finish();
                }
                
            } catch (Exception e) {
                logger.error("读取Excel文件信息失败：{}", e.getMessage(), e);
                fileInfo.setValid(false);
                fileInfo.setErrorMessage("读取文件信息失败：" + e.getMessage());
            }
            
        } catch (Exception e) {
            logger.error("获取Excel文件信息时发生异常：{}", e.getMessage(), e);
            fileInfo.setValid(false);
            fileInfo.setErrorMessage("获取文件信息异常：" + e.getMessage());
        }
        
        return fileInfo;
    }
    
    /**
     * 合并导入结果
     */
    private <T> void mergeImportResults(ExcelImportResult<T> target, ExcelImportResult<T> source) {
        target.setTotalRows(target.getTotalRows() + source.getTotalRows());
        target.setDataRows(target.getDataRows() + source.getDataRows());
        target.setSuccessRows(target.getSuccessRows() + source.getSuccessRows());
        target.setFailureRows(target.getFailureRows() + source.getFailureRows());
        target.setSkippedRows(target.getSkippedRows() + source.getSkippedRows());

        if (source.getSuccessData() != null) {
            target.getSuccessData().addAll(source.getSuccessData());
        }

        if (source.getFailureData() != null) {
            target.getFailureData().addAll(source.getFailureData());
        }

        if (source.getErrors() != null) {
            target.getErrors().addAll(source.getErrors());
        }
    }
}
