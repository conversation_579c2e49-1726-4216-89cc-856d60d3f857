package com.xlc.excel.service.impl;

import com.xlc.excel.enums.ExcelTemplateEnum;
import com.xlc.excel.service.ExcelImportService;
import com.xlc.excel.service.ExcelImportService.ExcelFileInfo;
import com.xlc.excel.service.ExcelUtilService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

/**
 * Excel工具服务实现类
 * 
 * <AUTHOR>
 * @since 2025-06-18
 */
@Service
public class ExcelUtilServiceImpl implements ExcelUtilService {
    
    private static final Logger logger = LoggerFactory.getLogger(ExcelUtilServiceImpl.class);
    
    @Autowired
    private ExcelImportService excelImportService;
    
    @Override
    public boolean validateExcelFormat(InputStream inputStream, String fileName, ExcelTemplateEnum template) {
        try {
            logger.debug("验证Excel文件格式：{}，模板：{}", fileName, template.getName());
            return excelImportService.validateExcelFormat(inputStream, fileName, template);
        } catch (Exception e) {
            logger.error("验证Excel文件格式失败：{}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean validateExcelFormat(InputStream inputStream, String fileName, String tableName) {
        try {
            logger.debug("验证Excel文件格式：{}，表名：{}", fileName, tableName);
            return excelImportService.validateExcelFormat(inputStream, fileName, tableName);
        } catch (Exception e) {
            logger.error("验证Excel文件格式失败：{}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public ExcelFileInfo getExcelFileInfo(InputStream inputStream, String fileName) {
        try {
            logger.debug("获取Excel文件信息：{}", fileName);
            return excelImportService.getExcelFileInfo(inputStream, fileName);
        } catch (Exception e) {
            logger.error("获取Excel文件信息失败：{}", e.getMessage(), e);
            
            // 返回错误信息
            ExcelFileInfo errorInfo = new ExcelFileInfo();
            errorInfo.setFileName(fileName);
            errorInfo.setValid(false);
            errorInfo.setErrorMessage("获取文件信息失败：" + e.getMessage());
            return errorInfo;
        }
    }
    
    @Override
    public List<ExcelTemplateEnum> getSupportedTemplates() {
        logger.debug("获取支持的模板列表");
        return Arrays.asList(ExcelTemplateEnum.values());
    }
    
    @Override
    public ExcelTemplateEnum getTemplateByTableName(String tableName) {
        logger.debug("根据表名获取模板：{}", tableName);
        return ExcelTemplateEnum.getByTableName(tableName);
    }
    
    @Override
    public boolean isExcelFile(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return false;
        }
        
        String extension = getFileExtension(fileName).toLowerCase();
        return "xls".equals(extension) || "xlsx".equals(extension);
    }
    
    @Override
    public String getFileExtension(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        
        return fileName.substring(lastDotIndex + 1);
    }
    
    @Override
    public String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
}
