package com.xlc.excel.service.impl;

import com.xlc.excel.dto.ExcelExportRequest;
import com.xlc.excel.dto.ExcelImportRequest;
import com.xlc.excel.dto.ExcelImportResult;
import com.xlc.excel.enums.ExcelTemplateEnum;
import com.xlc.excel.service.ExcelExportService;
import com.xlc.excel.service.ExcelImportService;
import com.xlc.excel.service.ExcelService;
import com.xlc.excel.service.ExcelHealthService;
import com.xlc.excel.service.ExcelUtilService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Excel服务主实现类
 * 整合导入导出功能的统一入口
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Service
public class ExcelServiceImpl implements ExcelService {

    private static final Logger logger = LoggerFactory.getLogger(ExcelServiceImpl.class);

    @Autowired
    private ExcelImportService excelImportService;

    @Autowired
    private ExcelExportService excelExportService;

    @Autowired
    private ExcelHealthService excelHealthService;

    @Autowired
    private ExcelUtilService excelUtilService;
    
    // ==================== 导入相关方法 ====================
    
    @Override
    public <T> ExcelImportResult<T> importExcel(ExcelImportRequest request) {
        excelHealthService.recordImportTaskStart();
        try {
            logger.info("开始导入Excel文件：{}", request.getFileName());
            return excelImportService.importExcel(request);
        } catch (Exception e) {
            excelHealthService.recordError("导入Excel文件失败：" + e.getMessage());
            throw e;
        } finally {
            excelHealthService.recordImportTaskEnd();
        }
    }
    
    @Override
    public <T> ExcelImportResult<T> importByTableName(InputStream inputStream, String fileName, String tableName, Class<T> targetClass) {
        excelHealthService.recordImportTaskStart();
        try {
            logger.info("根据表名导入Excel文件：{}，表名：{}", fileName, tableName);
            return excelImportService.importByTableName(inputStream, fileName, tableName, targetClass);
        } catch (Exception e) {
            excelHealthService.recordError("根据表名导入Excel文件失败：" + e.getMessage());
            throw e;
        } finally {
            excelHealthService.recordImportTaskEnd();
        }
    }
    
    @Override
    public <T> ExcelImportResult<T> importByTemplate(InputStream inputStream, String fileName, ExcelTemplateEnum template, Class<T> targetClass) {
        excelHealthService.recordImportTaskStart();
        try {
            logger.info("根据模板导入Excel文件：{}，模板：{}", fileName, template.getName());
            return excelImportService.importByTemplate(inputStream, fileName, template, targetClass);
        } catch (Exception e) {
            excelHealthService.recordError("根据模板导入Excel文件失败：" + e.getMessage());
            throw e;
        } finally {
            excelHealthService.recordImportTaskEnd();
        }
    }
    
    @Override
    public <T> ExcelImportResult<T> previewExcel(InputStream inputStream, String fileName, Class<T> targetClass, int previewRows) {
        excelHealthService.recordImportTaskStart();
        try {
            logger.info("预览Excel文件：{}，预览行数：{}", fileName, previewRows);
            return excelImportService.previewExcel(inputStream, fileName, targetClass, previewRows);
        } catch (Exception e) {
            excelHealthService.recordError("预览Excel文件失败：" + e.getMessage());
            throw e;
        } finally {
            excelHealthService.recordImportTaskEnd();
        }
    }
    
    @Override
    public <T> CompletableFuture<ExcelImportResult<T>> importExcelAsync(ExcelImportRequest request) {
        excelHealthService.recordImportTaskStart();
        logger.info("开始异步导入Excel文件：{}", request.getFileName());

        CompletableFuture<ExcelImportResult<T>> future = excelImportService.<T>importExcelAsync(request);
        return future.whenComplete((result, throwable) -> {
                    excelHealthService.recordImportTaskEnd();
                    if (throwable != null) {
                        excelHealthService.recordError("异步导入Excel文件失败：" + throwable.getMessage());
                    }
                });
    }
    
    // ==================== 导出相关方法 ====================
    
    @Override
    public <T> ExcelExportService.ExcelExportResult exportExcel(ExcelExportRequest<T> request) {
        excelHealthService.recordExportTaskStart();
        try {
            logger.info("开始导出Excel文件：{}", request.getFileName());
            return excelExportService.exportExcel(request);
        } catch (Exception e) {
            excelHealthService.recordError("导出Excel文件失败：" + e.getMessage());
            throw e;
        } finally {
            excelHealthService.recordExportTaskEnd();
        }
    }
    
    @Override
    public <T> ExcelExportService.ExcelExportResult exportByTableName(OutputStream outputStream, String fileName, String tableName, List<T> data, Class<T> dataClass) {
        excelHealthService.recordExportTaskStart();
        try {
            logger.info("根据表名导出Excel文件：{}，表名：{}，数据行数：{}", fileName, tableName, data != null ? data.size() : 0);
            return excelExportService.exportByTableName(outputStream, fileName, tableName, data, dataClass);
        } catch (Exception e) {
            excelHealthService.recordError("根据表名导出Excel文件失败：" + e.getMessage());
            throw e;
        } finally {
            excelHealthService.recordExportTaskEnd();
        }
    }
    
    @Override
    public <T> ExcelExportService.ExcelExportResult exportByTemplate(OutputStream outputStream, String fileName, ExcelTemplateEnum template, List<T> data, Class<T> dataClass) {
        excelHealthService.recordExportTaskStart();
        try {
            logger.info("根据模板导出Excel文件：{}，模板：{}，数据行数：{}", fileName, template.getName(), data != null ? data.size() : 0);
            return excelExportService.exportByTemplate(outputStream, fileName, template, data, dataClass);
        } catch (Exception e) {
            excelHealthService.recordError("根据模板导出Excel文件失败：" + e.getMessage());
            throw e;
        } finally {
            excelHealthService.recordExportTaskEnd();
        }
    }
    
    @Override
    public ExcelExportService.ExcelExportResult exportTemplate(OutputStream outputStream, String fileName, ExcelTemplateEnum template) {
        excelHealthService.recordExportTaskStart();
        try {
            logger.info("导出Excel模板文件：{}，模板：{}", fileName, template.getName());
            return excelExportService.exportTemplate(outputStream, fileName, template);
        } catch (Exception e) {
            excelHealthService.recordError("导出Excel模板文件失败：" + e.getMessage());
            throw e;
        } finally {
            excelHealthService.recordExportTaskEnd();
        }
    }
    
    @Override
    public <T> ExcelExportService.ExcelExportResult exportTemplate(OutputStream outputStream, String fileName, String tableName, Class<T> dataClass) {
        excelHealthService.recordExportTaskStart();
        try {
            logger.info("导出Excel模板文件：{}，表名：{}", fileName, tableName);
            return excelExportService.exportTemplate(outputStream, fileName, tableName, dataClass);
        } catch (Exception e) {
            excelHealthService.recordError("导出Excel模板文件失败：" + e.getMessage());
            throw e;
        } finally {
            excelHealthService.recordExportTaskEnd();
        }
    }
    
    @Override
    public <T> CompletableFuture<ExcelExportService.ExcelExportResult> exportExcelAsync(ExcelExportRequest<T> request) {
        excelHealthService.recordExportTaskStart();
        logger.info("开始异步导出Excel文件：{}", request.getFileName());

        return excelExportService.exportExcelAsync(request)
                .whenComplete((result, throwable) -> {
                    excelHealthService.recordExportTaskEnd();
                    if (throwable != null) {
                        excelHealthService.recordError("异步导出Excel文件失败：" + throwable.getMessage());
                    }
                });
    }
    
    // ==================== 工具方法 ====================

    @Override
    public boolean validateExcelFormat(InputStream inputStream, String fileName, ExcelTemplateEnum template) {
        return excelUtilService.validateExcelFormat(inputStream, fileName, template);
    }

    @Override
    public boolean validateExcelFormat(InputStream inputStream, String fileName, String tableName) {
        return excelUtilService.validateExcelFormat(inputStream, fileName, tableName);
    }

    @Override
    public ExcelImportService.ExcelFileInfo getExcelFileInfo(InputStream inputStream, String fileName) {
        return excelUtilService.getExcelFileInfo(inputStream, fileName);
    }

    @Override
    public List<ExcelTemplateEnum> getSupportedTemplates() {
        return excelUtilService.getSupportedTemplates();
    }

    @Override
    public ExcelTemplateEnum getTemplateByTableName(String tableName) {
        return excelUtilService.getTemplateByTableName(tableName);
    }
    
    @Override
    public ServiceHealthInfo getServiceHealth() {
        return excelHealthService.getServiceHealth();
    }
}
