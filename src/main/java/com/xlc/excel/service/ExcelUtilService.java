package com.xlc.excel.service;

import com.xlc.excel.enums.ExcelTemplateEnum;
import com.xlc.excel.service.ExcelImportService.ExcelFileInfo;

import java.io.InputStream;
import java.util.List;

/**
 * Excel工具服务接口
 * 提供Excel文件验证、信息获取、模板管理等工具功能
 * 
 * <AUTHOR>
 * @since 2025-06-18
 */
public interface ExcelUtilService {
    
    /**
     * 验证Excel文件格式
     * 
     * @param inputStream 文件输入流
     * @param fileName 文件名
     * @param template 模板标识
     * @return 验证结果
     */
    boolean validateExcelFormat(InputStream inputStream, String fileName, ExcelTemplateEnum template);
    
    /**
     * 验证Excel文件格式
     * 
     * @param inputStream 文件输入流
     * @param fileName 文件名
     * @param tableName 表名
     * @return 验证结果
     */
    boolean validateExcelFormat(InputStream inputStream, String fileName, String tableName);
    
    /**
     * 获取Excel文件信息
     * 
     * @param inputStream 文件输入流
     * @param fileName 文件名
     * @return Excel文件信息
     */
    ExcelFileInfo getExcelFileInfo(InputStream inputStream, String fileName);
    
    /**
     * 获取支持的模板列表
     * 
     * @return 模板列表
     */
    List<ExcelTemplateEnum> getSupportedTemplates();
    
    /**
     * 根据表名获取对应的模板
     * 
     * @param tableName 表名
     * @return 模板标识
     */
    ExcelTemplateEnum getTemplateByTableName(String tableName);
    
    /**
     * 检查文件是否为Excel格式
     * 
     * @param fileName 文件名
     * @return 是否为Excel文件
     */
    boolean isExcelFile(String fileName);
    
    /**
     * 获取文件扩展名
     * 
     * @param fileName 文件名
     * @return 文件扩展名
     */
    String getFileExtension(String fileName);
    
    /**
     * 格式化文件大小
     * 
     * @param bytes 字节数
     * @return 格式化后的文件大小
     */
    String formatFileSize(long bytes);
}
